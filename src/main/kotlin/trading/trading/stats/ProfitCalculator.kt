package trading.trading.stats

import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.databind.ObjectMapper
import io.lettuce.core.api.StatefulRedisConnection
import io.micronaut.runtime.event.ApplicationShutdownEvent
import io.micronaut.runtime.event.annotation.EventListener
import io.micronaut.scheduling.TaskScheduler
import io.micronaut.scheduling.annotation.Scheduled
import jakarta.annotation.PostConstruct
import jakarta.inject.Singleton
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.cloudwatch.model.StandardUnit.NONE
import trading.arb.metrics.Metrics.Companion.logMetric
import trading.events.*
import trading.events.SettlementEvent.SettlementType.Funding
import trading.events.SettlementEvent.SettlementType.Interest
import trading.exceptions.scheduleCatching
import trading.exchanges.ExchangeRouter
import trading.metrics.MetricsLabels.Companion.PROFIT_DELTA
import trading.models.FeeAsset
import trading.models.FeeAsset.Companion.BnbFeeAsset
import trading.models.FeeAsset.Companion.CoinFeeAsset
import trading.models.FeeAsset.Companion.UsdFeeAsset
import trading.models.FeeAsset.Companion.bnbFeeToCoinFee
import trading.models.FeeAsset.Companion.bnbFeeToUsdtFee
import trading.models.FeeAsset.Companion.coinFeeToUsdtFee
import trading.models.FeeAsset.Companion.usdtFeeToCoinFee
import trading.models.Strategy
import trading.models.StrategyId
import trading.models.SymbolEx
import trading.storage.repos.FirstLegPositionRepository
import trading.storage.repos.RedisConnector
import trading.storage.repos.SecondLegPositionRepository
import trading.storage.repos.StrategyRepository
import trading.trading.common.resolveUsdKoef
import trading.trading.helpers.QuotesResolver
import trading.trading.holders.OrderBookHolder
import trading.utils.*
import java.lang.System.currentTimeMillis
import java.time.Duration
import java.time.Duration.ofMillis
import java.time.Instant
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.util.Collections
import java.util.concurrent.ConcurrentHashMap
import kotlin.collections.HashMap
import kotlin.math.absoluteValue
import kotlin.math.max

@Singleton
class ProfitCalculator(
    private val profitsFromPositions: ProfitsFromPositions,
    private val connection: StatefulRedisConnection<String, String>,
    private val strategyRepository: StrategyRepository,
    private val orderBookHolder: OrderBookHolder,
    private val quotesResolver: QuotesResolver,
    private val firstLegPositionRepository: FirstLegPositionRepository,
    private val secondLegPositionRepository: SecondLegPositionRepository,
    private val eventPublisher: EventPublisher,
    private val taskScheduler: TaskScheduler,
    private val exchangeRouter: ExchangeRouter,
    objectMapper: ObjectMapper
) : EventConsumer, RedisConnector(objectMapper) {

    companion object {
        private const val PROFIT_NAMESPACE = "profits" // old namespace 'profit' is deprecated
        private const val FUNDING_NAMESPACE = "funding"
        private const val INTEREST_NAMESPACE = "interest"
        private const val HISTORY_NAMESPACE = "profits-history"

        private const val HISTORY_SIZE = 20

        private val log = LoggerFactory.getLogger(this::class.java)
    }

    private val profits = ConcurrentHashMap<StrategyId, ProfitAndFee>()

    // asset -> accumulated funding
    private val fundings = ConcurrentHashMap<String, Double>()

    // asset -> accumulated interest
    private val interests = ConcurrentHashMap<String, Double>()

    private val bnbQuote = ExpiringObject(60 * 30 * 1000) { quotesResolver.getBnbPrice() } // 30 minutes cache

    private val ackedSettlement: MutableSet<Int> = Collections.synchronizedSet(MaxSizeHashMap.newSet(30))

    @Volatile
    private var settled = false

    override val subscriptions: Array<Subscription<*>> = arrayOf(
        Subscription(TradeReportEvent::class.java) { event -> this.captureTradeReport(event) },
        Subscription(SettlementEvent::class.java) { event -> this.captureSettlement(event) }
    )

    fun reset() {
        profits.clear()
        fundings.clear()
        interests.clear()
        remove()
        init()
    }

    fun resetFundings() {
        fundings.clear()
        connection.sync().del(FUNDING_NAMESPACE)
    }

    fun resetInterests() {
        interests.clear()
        connection.sync().del(INTEREST_NAMESPACE)
    }

    fun reset(strategyId: String) {
        profits.remove(strategyId)
            ?.also {
                persistToHistory(strategyRepository.findById(strategyId), it)
            }
        remove(strategyId)
    }

    private fun persistToHistory(strategy: Strategy, profit: ProfitAndFee) {
        if (profit.pnl == 0.0) {
            return
        }

        getHistory().orEmpty().takeLast(HISTORY_SIZE - 1).toMutableList().apply {
            add(ProfitHistory(strategy.toShortString(), profit.pnl - profit.fee, currentTimeMillis()))
        }.let { prevProfits ->
            connection.async().set(HISTORY_NAMESPACE, prevProfits.toJson())
        }
    }

    fun getHistory(): List<ProfitHistory>? = connection
        .sync()
        .get(HISTORY_NAMESPACE)
        ?.toObj(object : TypeReference<List<ProfitHistory>>() {})

    @Scheduled(fixedDelay = "30s", initialDelay = "30s")
    fun settlePnL() {
        if (settled) {
            return
        }

        fun isTrading(strategyId: String) = firstLegPositionRepository.find(strategyId).active()
                || secondLegPositionRepository.find(strategyId).active()

        settled = profits.keys.map { key ->
            profits.compute(key) { _, value ->
                if (value == null || value.unrealizedPnl == 0.0 || isTrading(key)) {
                    value
                } else {
                    strategyRepository.findById(key).let { str ->
                        value.realizePnl((orderBookHolder.getLastQuote(str.legs.leg2.symbolEx, false)!!.ask)).also {
                            eventPublisher.publishAsync(ProfitUpdateEvent(str.uuid, it))
                        }.also {
                            logMetric(
                                PROFIT_DELTA, it.pnl - value.pnl,
                                str.legs.leg1.symbolEx.exchange, str.legs.leg1.symbolEx.symbol,
                                mapOf("exchange2" to str.legs.leg2.symbolEx.exchange, "str" to str.uuid),
                                unit = NONE
                            )
                        }
                    }
                }
            }?.unrealizedPnl == 0.0
        }.all { it } // all true

        persist()
    }

    fun isPnlZero(strategyId: String): Boolean = (profits[strategyId]?.pnl?.compareTo(0.0) ?: 0) == 0

    fun isPnlNonNegative(strategyId: String): Boolean = (profits[strategyId]?.pnl?.compareTo(0.0) ?: 0) >= 0

    fun isUpnlZero(strategyId: String): Boolean = (profits[strategyId]?.unrealizedPnl?.compareTo(0.0) ?: 0) == 0

    fun isUpnlNonNegative(strategyId: String): Boolean = (profits[strategyId]?.unrealizedPnl?.compareTo(0.0) ?: 0) >= 0

    fun isProfitable(strategyId: String): Boolean = (profits[strategyId]?.pnl?.compareTo(0.0) ?: 0) > 0

    @PostConstruct
    fun init() {
        loadFundings()?.let { this.fundings.putAll(it) }
        loadInterests()?.let { this.interests.putAll(it) }

        loadProfits()
            .filter { (strategyId, _) -> strategyRepository.contains(strategyId) } // removing non existent
            .let { profits.putAll(it) }

        actualizeProfits()
    }

    private fun actualizeProfits() {
        // if not present in profits map, then it means either it's a new strategy or profit was reset
        // so we need to restore positions (if we have something on exchange)
        profitsFromPositions.getPositionsCurSizes(true)
            .filterNot { (strategyId, _) -> profits.containsKey(strategyId) }
            .forEach { (strategyId, pos) ->
                val (firstPos, secondPos) = pos
                captureTrade(
                    profits, strategyId, firstPos.price,
                    firstPos.usdSize.costFor(firstPos.price),
                    firstPos.usdSize, 0.0, UsdFeeAsset
                )
                captureTrade(
                    profits, strategyId, secondPos.price,
                    secondPos.usdSize.costFor(secondPos.price),
                    secondPos.usdSize, 0.0, UsdFeeAsset
                )
            }

        strategyRepository.findAll().forEach { str ->
            // initializing profits for all strategies
            profits.computeIfAbsent(str.uuid) { ProfitAndFee(isCoined(str.uuid)) }
        }

        taskScheduler.scheduleCatching(this, Duration.ofSeconds(3), null) {
            // TODO requires proper fix?
            // this has to executed after, otherwise there's a cyclic dependency on self
            // when initializing asyncEventPublisher listeners
            profits.forEach { (strId, profitAndFee) ->
                eventPublisher.publishAsync(ProfitUpdateEvent(strId, profitAndFee, true))
            }
        }
    }

    @EventListener
    fun persistOnShutdown(event: ApplicationShutdownEvent) {
        persistSync()
    }

    private fun loadProfits(): Map<StrategyId, ProfitAndFee> = connection
        .sync()
        .hgetall(PROFIT_NAMESPACE)
        .mapValues { (_, value) -> value.toObj(ProfitAndFee::class.java) }

    private fun loadFundings(): Map<String, Double>? = connection
        .sync()
        .get(FUNDING_NAMESPACE)
        ?.toObj(object : TypeReference<Map<String, Double>>() {})

    private fun loadInterests(): Map<String, Double>? = connection
        .sync()
        .get(INTEREST_NAMESPACE)
        ?.toObj(object : TypeReference<Map<String, Double>>() {})

    private fun persist() {
        profits.forEach { (strategyId, profit) ->
            connection.async().hset(PROFIT_NAMESPACE, strategyId, profit.toJson())
        }
    }

    private fun persistSync() {
        profits.forEach { (strategyId, profit) ->
            connection.sync().hset(PROFIT_NAMESPACE, strategyId, profit.toJson())
        }
    }

    private fun remove(strategyId: String) {
        connection.sync().hdel(PROFIT_NAMESPACE, strategyId)
    }

    private fun remove() {
        connection.sync().del(PROFIT_NAMESPACE)
        connection.sync().del(FUNDING_NAMESPACE)
        connection.sync().del(INTEREST_NAMESPACE)
    }

    private fun captureSettlement(event: SettlementEvent) {
        if (event.symbolEx != null
            && !strategyRepository.findSymbolsByExchange(event.symbolEx.exchange).contains(event.symbolEx.symbol)
        ) {
            return // not this bot's strategy
        }

        val hash = (event.symbolEx?.hashCode() ?: 0) + event.amount.hashCode()

        if (!ackedSettlement.add(hash)) {
            return // already existed
        }

        log.info("Got settlement: $event")

        val asset = if (
            (event.asset.length == 4 && event.asset.startsWith("USD", true))  // USDC, USDT
            || event.asset == "BNFCR" // binance futures credits
        ) "USD" else
            event.asset

        if (asset != "USD") return // TODO uncomment after ARB-388

        when (event.type) {
            Funding -> {
                fundings.compute(asset) { _, prev -> prev.orZero() + event.amount }
                connection.async().set(FUNDING_NAMESPACE, fundings.toJson())
            }

            Interest -> {
                interests.compute(asset) { _, prev -> prev.orZero() + event.amount }
                connection.async().set(INTEREST_NAMESPACE, interests.toJson())
            }
        }
    }

    private fun captureTradeReport(event: TradeReportEvent) {
        val rep = event.report
        captureTrade(
            profits,
            rep.strategyId,
            rep.price,
            rep.usdSize.costFor(rep.price),
            rep.usdSize,
            rep.fee.normalize(rep.symbolEx),
            rep.feeAsset
        )
        settled = false
    }

    private fun Double.normalize(symbolEx: SymbolEx): Double {
        // only for bitmex and non btc
        if (symbolEx.exchange != "bitmex") { // TODO Better to calc commision from bitmex comm percent in execution
            return this
        }

        return if (symbolEx.natural) {
            // fee is sent in contracts
            exchangeRouter.contractsToUsd(symbolEx, this)
        } else {
            (this / strategyRepository.resolveUsdKoef(symbolEx)).scaleTo(8)
        }
    }

    private fun isCoined(strategyId: String): Boolean {
        fun isCoined(symbolEx: SymbolEx) = when {
            symbolEx.symbol.contains("USDT") -> false
            symbolEx.symbol.endsWith("USDC") -> false
            symbolEx.exchange == "hyperliquid" -> false
            symbolEx.exchange == "bybit-usdt" && symbolEx.symbol.endsWith("PERP") -> false
            else -> true
        }

        val legs = strategyRepository.findById(strategyId).legs
        return isCoined(legs.leg1.symbolEx) || isCoined(legs.leg2.symbolEx)
    }

    private fun convertFee(coined: Boolean, price: Double, fee: Double, feeAsset: FeeAsset): Double {
        if (fee == 0.0) {
            return 0.0
        }

        return when {
            coined -> {
                when (feeAsset) {
                    UsdFeeAsset -> fee.usdtFeeToCoinFee(price)
                    CoinFeeAsset -> fee
                    BnbFeeAsset -> fee.bnbFeeToCoinFee(bnbQuote.get(), price)
                    else -> error("Unknown fee asset")
                }
            }

            else -> {
                when (feeAsset) {
                    UsdFeeAsset -> fee
                    CoinFeeAsset -> fee.coinFeeToUsdtFee(price)
                    BnbFeeAsset -> fee.bnbFeeToUsdtFee(bnbQuote.get())
                    else -> error("Unknown fee asset")
                }
            }
        }
    }

    /**
     * buy counts with -, sell with +
     */
    private fun captureTrade(
        profits: MutableMap<StrategyId, ProfitAndFee>, strategyId: String, price: Double,
        cost: Double, usdSize: Double, fee: Double, feeAsset: FeeAsset
    ) {
        profits.compute(strategyId) { _, prevProfit ->
            val coined = prevProfit?.coined ?: isCoined(strategyId)

            val resFee = convertFee(coined, price, fee, feeAsset)

            prevProfit?.adjust(cost, resFee, usdSize.absoluteValue)
                ?: ProfitAndFee(coined, 0.0, cost, resFee, usdSize.absoluteValue)
        }

        persist()
    }

    fun getFundings() = fundings

    fun getInterests() = interests

    fun getProfits(): Map<StrategyId, HumanReadableProfit> {
        val copy = HashMap(profits)

        profitsFromPositions.getPositionsCurSizes(false).forEach { (strategyId, pos) ->
            val (firstPos, secondPos) = pos
            if (firstPos.usdSize.absoluteValue > 0.0 && secondPos.usdSize.absoluteValue > 0.0) {
                captureTrade(
                    copy, strategyId, firstPos.price,
                    firstPos.usdSize.costFor(firstPos.price) * -1.0,
                    firstPos.usdSize.absoluteValue,
                    0.0, UsdFeeAsset
                )
                captureTrade(
                    copy, strategyId, secondPos.price,
                    secondPos.usdSize.costFor(secondPos.price) * -1.0,
                    secondPos.usdSize.absoluteValue,
                    0.0, UsdFeeAsset
                )
            }
        }

        return copy.mapValues {
            strategyRepository.findById(it.key).let { str ->
                HumanReadableProfit(it.value)
                    .withPrice(orderBookHolder.getLastQuote(str.legs.leg2.symbolEx, true)!!.ask)
                    .withStrategy(str)
            }
        }
    }

    private fun Double.costFor(price: Double): Double =
        if (price == 0.0) 0.0 else (this / price).scaleTo(8)

    fun markNotified(strategyId: String) {
        profits.compute(strategyId) { _, profit ->
            profit?.copy(notifiedAt = currentTimeMillis(), notifiedTotalPnl = profit.pnl - profit.fee)
        }
        persist()
    }
}

data class ProfitAndFee(
    val coined: Boolean = true,
    val pnl: Double = 0.0,
    val unrealizedPnl: Double = 0.0,
    val fee: Double = 0.0,
    val usdVolume: Double = 0.0,
    val startedAt: Long = currentTimeMillis(), // when we started to track this profit
    val notifiedAt: Long = startedAt, // last time we notified about this profit
    val notifiedTotalPnl: Double = 0.0 // pnl + fee that we notified about
) {
    override fun toString() = "PnL = $pnl, unrealized pnl = $unrealizedPnl, fee = $fee, usdVolume = $usdVolume"

    fun adjust(cost: Double, fee: Double, usdVolume: Double) = this.copy(
        unrealizedPnl = this.unrealizedPnl.plus(cost),
        fee = this.fee.plus(fee),
        usdVolume = this.usdVolume.plus(usdVolume)
    )

    fun realizePnl(price: Double): ProfitAndFee = if (this.coined) {
        this.copy(pnl = pnl + unrealizedPnl, unrealizedPnl = 0.0)
    } else {
        this.copy(pnl = pnl + unrealizedPnl * price, unrealizedPnl = 0.0)
    }

    /**
     * @return raw PnL without scaling or rounding
     */
    fun rawPnlPerHour(): Double {
        val timeDiff = ofMillis(currentTimeMillis() - startedAt)
        val hours = max(timeDiff.toHours().toInt(), 1)
        return pnl / hours
    }
}

data class HumanReadableProfit(val profitAndFee: ProfitAndFee) {

    companion object {
        const val USD_SCALE = 2
    }

    lateinit var strategy: Strategy

    private var price: Double = 0.0

    private val scale by lazy {
        if (profitAndFee.pnl.absoluteValue > 1.0 || profitAndFee.unrealizedPnl.absoluteValue > 1.0) USD_SCALE else 5
    }

    private val sign by lazy {
        if ((strategy.legs.leg1.symbolEx.symbol.startsWith("BTC") ||
                    strategy.legs.leg2.symbolEx.symbol.startsWith("BTC"))
        ) {
            "\u20BF"
        } else if (strategy.legs.leg1.symbolEx.symbol.startsWith("ETH")) {
            "\u27E0"
        } else {
            "\u20AE"
        }
    }

    private val totalPnl: Double by lazy {
        (profitAndFee.pnl - profitAndFee.fee).scaleTo(scale)
    }

    val totalUsdPnl: Double by lazy {
        if (profitAndFee.coined) totalPnl * price else totalPnl
    }

    val notifiedTotalUsdPnl: Double by lazy {
        if (profitAndFee.coined) profitAndFee.notifiedTotalPnl * price else profitAndFee.notifiedTotalPnl
    }

    val total: Double by lazy {
        if (profitAndFee.unrealizedPnl == 0.0) {
            totalPnl
        } else if (profitAndFee.coined) {
            totalPnl + profitAndFee.unrealizedPnl
        } else {
            totalPnl + profitAndFee.unrealizedPnl * price
        }.scaleTo(scale)
    }

    fun usdPnlDelta(): Double = (totalPnl - this.profitAndFee.notifiedTotalPnl).let {
        if (!profitAndFee.coined) it else it * price
    }

    fun withPrice(price: Double): HumanReadableProfit {
        this.price = price
        return this
    }

    fun withStrategy(str: Strategy): HumanReadableProfit {
        this.strategy = str
        return this
    }

    override fun toString(): String = buildString {
        append(strategy.toShortString())

        val timeDiff = ofMillis(currentTimeMillis() - profitAndFee.startedAt)
        val perHour = profitAndFee.rawPnlPerHour()

        append(" for last ${timeDiff.toHumanReadable()} ($${perHour.scaleTo(USD_SCALE).toPrettyString()} per hour)\n")

        if (profitAndFee.coined) {
            append(" - Fee: ${sign}${profitAndFee.fee.scaleTo(scale)}\n")
            append(" - Volume: $${profitAndFee.usdVolume.scaleTo(0).toPrettyString()}\n")
            append(" - PnL: ${sign}${profitAndFee.pnl.scaleTo(scale)}\n")
            if (profitAndFee.unrealizedPnl != 0.0) {
                append(" - Unrealized PnL: ${profitAndFee.unrealizedPnl.scaleTo(scale)}\n")
            }
        } else {
            append(" - Fee: $${profitAndFee.fee.scaleTo(USD_SCALE)}\n")
            append(" - Volume: $${profitAndFee.usdVolume.scaleTo(0).toPrettyString()}\n")
            val unrealizedPnlUsd = (profitAndFee.unrealizedPnl * price).scaleTo(USD_SCALE)
            append(" - PnL: $${profitAndFee.pnl.scaleTo(USD_SCALE)}\n")
            if (profitAndFee.unrealizedPnl != 0.0) {
                append(" - Unrealized PnL: $${unrealizedPnlUsd} (₮${profitAndFee.unrealizedPnl.scaleTo(scale)})\n")
            }
        }

        append(" <b>Total</b>: ${toStringTotal()}")
    }

    private fun toStringTotal() =
        if (profitAndFee.coined) "${sign}${total.toPrettyString()} ($${(total * price).scaleTo(USD_SCALE)})"
        else "$${total.scaleTo(USD_SCALE)}"

    fun toShortString() = "${strategy.toShortString()}: ${toStringTotal()}"
}

data class ProfitHistory(val strategy: String, val pnl: Double, val deleteAt: Long) {
    companion object {
        private val formatter = DateTimeFormatter.ofPattern("MM.dd (HH:mm)")

        private fun Double.pnlPicture() = when {
            this > 0.0 -> "💎"
            this < 0.0 -> "❌"
            else -> "➖"
        }
    }

    override fun toString(): String = buildString {
        append(pnl.pnlPicture())
        append(strategy)
        append(": ")
        append(pnl.toPrettyString(2))
        append(" [reset on ")
        append(formatter.format(Instant.ofEpochMilli(deleteAt).atZone(ZoneId.systemDefault()).toLocalDateTime()))
        append("]")
    }
}
