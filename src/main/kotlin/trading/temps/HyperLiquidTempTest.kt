package trading.temps

import io.micronaut.context.event.StartupEvent
import io.micronaut.runtime.event.annotation.EventListener
import jakarta.inject.Singleton
import kotlinx.coroutines.runBlocking
import trading.exchanges.ExchangeRouter
import trading.exchanges.hyperliquid.HyperLiquidConnector
import trading.models.ExecInst
import trading.models.Interval
import trading.models.Side
import trading.models.SymbolEx
import trading.utils.Seconds
import java.util.Collections.singletonList

//@Singleton
class HyperLiquidTempTest(
    private val exchangeRouter: ExchangeRouter, private val hyperLiquidConnector: HyperLiquidConnector
) {

//    @EventListener
    fun onStart(event: StartupEvent) {
        runBlocking {
            val symbolEx = SymbolEx("hyperliquid", "ETH")
            val seconds = Seconds()

            exchangeRouter.getRiskLimits(symbolEx).also {
                println("Risk limits: $it")
            }
            exchangeRouter.setLeverage(symbolEx, 5)
            exchangeRouter.getCandles(symbolEx, 120, Interval.ONE_MINUTE).also {
                println("candles (count: ${it.size})")
                println(it.joinToString("\n"))
            }

            exchangeRouter.getQuote(symbolEx).also { println("quote: $it") }
            exchangeRouter.getBalances().also { println("balances: $it") }
            exchangeRouter.getInstrument(symbolEx).also { println("instrument: $it") }

            hyperLiquidConnector.getPositions(singletonList(symbolEx.symbol)).also { println("positions: $it") }

            exchangeRouter.marketInUsd(
                symbolEx, 50.0, Side.Buy, "333fff", execInst = listOf()
            ).also {
                println("market: $it")
            }.also {
                exchangeRouter.getExecutions("wrong", it.exOrderId, symbolEx).also { trades ->
                    println("trades: $trades")
                }
            }.also {
                hyperLiquidConnector.getPositions(singletonList(symbolEx.symbol)).also { println("positions: $it") }
            }.also {
                exchangeRouter.marketInUsd(
                    symbolEx, 50.0, Side.Sell, "333fff", execInst = listOf(ExecInst.ReduceOnly)
                )
            }

            exchangeRouter.limitInContracts(
                symbolEx, 0.01, 1000.0, Side.Buy, "abc123", execInst = listOf(), requestId = 111
            ).also {
                println("limit: $it")
            }.also {
                exchangeRouter.getOrders(symbolEx, true).also {
                    println("orders: $it")
                }
            }.also {
                exchangeRouter.getOrder(it.botOrderId, null, symbolEx).also { fullOrder ->
                    println("order by bot id: $fullOrder")
                }
            }.also {
                exchangeRouter.getOrder("wrong", it.exOrderId, symbolEx).also { fullOrder ->
                    println("order by ex id: $fullOrder")
                }
            }.also {
                exchangeRouter.cancelOrder("wrong", it.exOrderId, symbolEx, "fromtest").also { canceledOrder ->
                    println("canceled by ex id: $canceledOrder")
                }
            }

            exchangeRouter.limitInContracts(
                symbolEx, 0.01, 1000.0, Side.Buy, "abc123", execInst = listOf(), requestId = 111
            ).also {
                hyperLiquidConnector.cancelAllOrders(singletonList(symbolEx.symbol))
            }

            // this one throws exception, because its not possible to get executions by botOrderId
            /*exchangeRouter.getExecutions(market.botOrderId, null, symbolEx).also {
                println("executions by bot id: $it")
                check(it.isNotEmpty()) { "Empty executions by bot id" }
            }*/

            /*exchangeRouter.getExecutions("wrong", market.exOrderId, symbolEx).also {
                println("executions by ex id: $it")
                check(it.isNotEmpty()) { "Empty executions by ex id" }
            }*/

            println("request time: $seconds")
        }
    }
}
