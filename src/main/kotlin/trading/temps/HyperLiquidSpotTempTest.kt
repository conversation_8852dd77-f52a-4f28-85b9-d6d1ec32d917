package trading.temps

import io.micronaut.context.event.StartupEvent
import io.micronaut.runtime.event.annotation.EventListener
import jakarta.inject.Singleton
import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking
import trading.exchanges.ExchangeRouter
import trading.exchanges.hyperliquid.HyperliquidSpotConnector
import trading.models.Interval
import trading.models.Side
import trading.models.SymbolEx
import trading.utils.Seconds
import java.util.Collections.singletonList
import kotlin.time.Duration.Companion.seconds

//@Singleton
class HyperLiquidSpotTempTest(
    private val exchangeRouter: ExchangeRouter,
    private val hyperliquidSpotConnector: HyperliquidSpotConnector
) {

//    @EventListener
    fun onStart(event: StartupEvent) {
        runBlocking {
            val symbolEx = SymbolEx("hyperliquid-spot", "UETH")
            val seconds = Seconds()
            exchangeRouter.getRiskLimits(symbolEx).also {
                println("Risk limits: $it")
            }

            exchangeRouter.setLeverage(symbolEx, 5)

            val candles = exchangeRouter.getCandles(symbolEx, 120, Interval.ONE_MINUTE)
            println("candles (count: ${candles.size}):")
            println(candles.joinToString("\n"))

            val quote = exchangeRouter.getQuote(symbolEx)
            println("quote: $quote")

            exchangeRouter.getBalances().also { println(it) }

            exchangeRouter.getInstrument(symbolEx).also { println("instrument: $it") }

            val positions = hyperliquidSpotConnector.getPositions(singletonList(symbolEx.symbol))
            println("positions: $positions")

            exchangeRouter.marketInUsd(
                symbolEx, 50.0, Side.Buy, "333fff", execInst = listOf()
            ).also {
                println("market: $it")
            }.also {
                val trades = exchangeRouter.getExecutions("wrong", it.exOrderId, symbolEx)
                println("trades: $trades")
            }

            exchangeRouter.limitInContracts(
                symbolEx, 0.01, 1000.0, Side.Buy, "abc123", execInst = listOf(), requestId = 100
            ).also {
                println("limit: $it")
            }.also {
                exchangeRouter.getOrders(symbolEx, true).also {
                    println("open orders: $it")
                }
            }.also {
                exchangeRouter.getOrder(it.botOrderId, null, symbolEx).also { fullOrder ->
                    println("order by bot id: $fullOrder")
                }
            }.also {
                exchangeRouter.getOrder("wrong", it.exOrderId, symbolEx).also { fullOrder ->
                    println("order by ex id: $fullOrder")
                }
            }.also {
                try {
                    exchangeRouter.updateOrder(symbolEx, it.botOrderId, null, 900.0, 0.1, emptyList(), 100)
                } catch (ex: IllegalStateException) {
                    println("order updating is not supported")
                }
            }.also {
                delay(1.seconds)
                hyperliquidSpotConnector.cancelAllOrders(singletonList(symbolEx.symbol))
            }


            exchangeRouter.limitInContracts(
                symbolEx, 0.01, 1000.0, Side.Buy, "abc123", execInst = listOf(), requestId = 100
            ).also {
                println("limit: $it")
                exchangeRouter.cancelOrder("wrong", it.exOrderId, symbolEx, "fromtest").also {
                    println("canceled by ex id: $it")
                }
            }

            exchangeRouter.limitInContracts(
                symbolEx, 0.01, 1000.0, Side.Buy, "abc123", execInst = listOf(), requestId = 100
            ).also {
                println("limit: $it")
                exchangeRouter.cancelOrder(it.botOrderId, null, symbolEx, "fromtest").also {
                    println("canceled by bot id: $it")
                }
            }

            println("request time: $seconds")
        }
    }
}
